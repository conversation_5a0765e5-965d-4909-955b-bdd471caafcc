'use client';

import React, { useState } from 'react';
import clsx from 'clsx';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import LockIcon from '@mui/icons-material/Lock';
import PaymentIcon from '@mui/icons-material/Payment';
import ReceiptIcon from '@mui/icons-material/Receipt';
import SecurityIcon from '@mui/icons-material/Security';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

const sidebarItems = [
	{
		href: '/account/home',
		label: 'Tài khoản',
		icon: <AccountCircleIcon />
	},
	{
		href: '/account/change-password',
		label: 'Đ<PERSON><PERSON> mật khẩu',
		icon: <LockIcon />
	},
	{
		href: '/account/security',
		label: 'Bảo mật',
		icon: <SecurityIcon />
	}
];

/**
 * The logo component.
 */

function Sidebar({ onCloseDrawer = () => {} }: { onCloseDrawer?: () => void }) {
	const pathname = usePathname();
	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
	const open = Boolean(anchorEl);

	const handleAvatarClick = (event: React.MouseEvent<HTMLElement>) => {
		setAnchorEl(event.currentTarget);
	};

	const handleClose = () => {
		setAnchorEl(null);
	};

	const handleLogout = () => {
		handleClose();
		// Xử lý đăng xuất ở đây
	};

	return (
		<List className="px-4">
			{sidebarItems.map((item, idx) => {
				const isActive = pathname === item.href;
				return (
					<ListItem
						disablePadding
						key={idx}
					>
						<ListItemButton
							component={Link}
							href={item.href}
							onClick={onCloseDrawer}
							className={clsx(
								'flex items-center justify-center rounded-lg transition-colors hover:text-foreground mb-2',
								{
									'!bg-[#e0e7eb] text-blue-600': isActive,
									'text-muted-foreground': !isActive
								}
							)}
						>
							<ListItemIcon
								className={clsx('transition-colors', {
									'text-blue-600': isActive
								})}
							>
								{item.icon}
							</ListItemIcon>
							<ListItemText primary={item.label} />
						</ListItemButton>
					</ListItem>
				);
			})}
		</List>
	);
}

export default Sidebar;
