import { createContext, useContext, useMemo, useState, useEffect } from 'react';
import { User } from '@auth/user';
import http from '@/lib/http';

export type AppContextType = {
	user: User | null;
	setUser: (user: User | null) => void;
	isGuest: boolean;
};
export const AppContext = createContext<AppContextType>({
	user: null,
	setUser: () => {},
	isGuest: true
});

export function AppProvider({ children }: { children: React.ReactNode }) {
	const [user, setUser] = useState<User | null>(null);
	const isGuest = useMemo(() => !user?.role || user?.role?.length === 0, [user]);

	useEffect(() => {
		const checkAuth = async () => {
			try {
				const checkRes = await http.get('/api-web/auth/check-token');
				console.log(checkRes);

				if (checkRes.statusCode === 200) {
					const profileRes = await http.get('/api-web/maps/users/current');
					if (profileRes.statusCode === 200) {
						setUser(profileRes.data);
					} else {
						setUser(null);
					}
				} else {
					setUser(null);
				}
			} catch (err) {
				console.log(err);

				setUser(null);
			}
		};
		checkAuth();
	}, []);

	return <AppContext.Provider value={{ user, setUser, isGuest }}>{children}</AppContext.Provider>;
}

export function useAppContext() {
	return useContext(AppContext);
}
