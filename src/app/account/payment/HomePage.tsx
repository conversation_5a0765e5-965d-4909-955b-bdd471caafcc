'use client';
import {
	Box,
	Typography,
	Paper,
	Divider,
	Button,
	List,
	ListItem,
	ListItemText,
	Avatar,
	IconButton,
	Chip
} from '@mui/material';
import PaymentIcon from '@mui/icons-material/Payment';
import AddCardIcon from '@mui/icons-material/AddCard';
import DeleteIcon from '@mui/icons-material/Delete';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

export default function PaymentPage() {
	// Dữ liệu mẫu phương thức thanh toán đã lưu
	const paymentMethods = [
		{
			id: 1,
			type: 'Visa',
			last4: '1234',
			expiry: '12/27',
			isDefault: true
		},
		{
			id: 2,
			type: 'MasterCard',
			last4: '5678',
			expiry: '09/26',
			isDefault: false
		}
	];

	const handleDelete = (id: number) => {
		// Xử lý xoá thẻ ở đây
	};

	return (
		<Box py={4}>
			<Typography
				variant="h5"
				fontWeight={700}
				mb={2}
			>
				<PERSON><PERSON><PERSON><PERSON> thức thanh toán
			</Typography>
			<Typography
				color="text.secondary"
				mb={2}
			>
				Quản lý các thẻ và phương thức thanh toán bạn đã lưu. Thẻ mặc định sẽ tự động được sử dụng khi đến hạn
				thanh toán.
			</Typography>

			<Box
				display="flex"
				justifyContent="flex-end"
				mb={2}
			>
				<Button
					startIcon={<AddCardIcon />}
					variant="contained"
					color="primary"
					sx={{ borderRadius: 2 }}
				>
					Thêm thẻ mới
				</Button>
			</Box>

			<Paper
				elevation={0}
				sx={{ boxShadow: 'none', mb: 3 }}
			>
				<List>
					{paymentMethods.map((method) => (
						<ListItem
							key={method.id}
							sx={{
								mb: 1,
								borderRadius: 2,
								bgcolor: method.isDefault ? '#e0e7eb' : 'transparent',
								alignItems: 'center'
							}}
							secondaryAction={
								<Box
									display="flex"
									alignItems="center"
									gap={1}
								>
									<IconButton
										size="small"
										color="info"
									>
										<InfoOutlinedIcon />
									</IconButton>
									{!method.isDefault && (
										<IconButton
											size="small"
											color="error"
											onClick={() => handleDelete(method.id)}
										>
											<DeleteIcon />
										</IconButton>
									)}
								</Box>
							}
						>
							<Avatar sx={{ bgcolor: '#1976d2', mr: 2 }}>
								<PaymentIcon />
							</Avatar>
							<ListItemText
								primary={
									<Box
										display="flex"
										alignItems="center"
										gap={1}
									>
										{`${method.type} **** ${method.last4}`}
										{method.isDefault && (
											<Chip
												label="Mặc định"
												size="small"
												color="primary"
											/>
										)}
									</Box>
								}
								secondary={`Hết hạn: ${method.expiry}`}
								primaryTypographyProps={{ fontWeight: 500 }}
							/>
						</ListItem>
					))}
				</List>
			</Paper>

			<Paper
				elevation={0}
				sx={{ p: 2, background: '#f6f8fa', boxShadow: 'none' }}
			>
				<Typography>
					Cần hỗ trợ về thanh toán?{' '}
					<Button
						color="primary"
						size="small"
						sx={{
							textTransform: 'none',
							p: 0,
							minWidth: 0
						}}
					>
						Liên hệ hỗ trợ
					</Button>
				</Typography>
			</Paper>
		</Box>
	);
}
