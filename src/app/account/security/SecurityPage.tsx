'use client';
import {
	Box,
	Typography,
	Paper,
	Button,
	Divider,
	Switch,
	List,
	ListItem,
	ListItemText,
	TextField
} from '@mui/material';
import { useState } from 'react';

export default function SecurityPage() {
	const [twoFAEnabled, setTwoFAEnabled] = useState(false);
	const [showOTPForm, setShowOTPForm] = useState(false);
	const [otp, setOtp] = useState('');
	const [otpError, setOtpError] = useState('');

	// G<PERSON><PERSON> lập danh sách thiết bị đăng nhập
	const devices = [
		{ id: 1, name: 'Chrome trên Windows', location: 'Hà Nội, Việt Nam', current: true },
		{ id: 2, name: 'Safari trên iPhone', location: 'TP.HCM, Việt Nam', current: false }
	];

	const handleToggle2FA = () => {
		if (!twoFAEnabled) {
			setShowOTPForm(true);
		} else {
			setTwoFAEnabled(false);
			setShowOTPForm(false);
		}
	};

	const handleConfirmOTP = () => {
		// Gi<PERSON> lập xác thực <PERSON>, thực tế sẽ gọi API kiểm tra mã OTP
		if (otp === '123456') {
			setTwoFAEnabled(true);
			setShowOTPForm(false);
			setOtp('');
			setOtpError('');
		} else {
			setOtpError('Mã xác thực không đúng');
		}
	};

	const handleLogoutDevice = (id: number) => {
		alert(`Đã đăng xuất thiết bị ID: ${id}`);
	};

	return (
		<Box py={4}>
			<Box sx={{ width: '100%' }}>
				<Typography
					variant="h5"
					fontWeight={700}
					mb={2}
				>
					Bảo mật tài khoản
				</Typography>
				<Divider sx={{ my: 3 }} />

				{/* 2FA */}
				<Paper
					sx={{ p: 3, mb: 4 }}
					variant="outlined"
				>
					<Typography
						variant="h6"
						fontWeight={600}
						mb={1}
					>
						Xác thực hai lớp (2FA)
					</Typography>
					<Typography
						color="text.secondary"
						mb={2}
					>
						Bật xác thực hai lớp để tăng cường bảo mật cho tài khoản của bạn.
					</Typography>
					<Box
						display="flex"
						alignItems="center"
						gap={2}
					>
						<Switch
							checked={twoFAEnabled}
							onChange={handleToggle2FA}
						/>
						<Typography>
							{twoFAEnabled ? 'Đã bật xác thực hai lớp' : 'Chưa bật xác thực hai lớp'}
						</Typography>
					</Box>
					{showOTPForm && (
						<Box
							mt={2}
							display="flex"
							flexDirection="column"
							gap={2}
							maxWidth={300}
						>
							<Typography color="primary">
								Nhập mã xác thực từ ứng dụng Google Authenticator (demo: 123456)
							</Typography>
							<TextField
								label="Mã xác thực"
								value={otp}
								onChange={(e) => setOtp(e.target.value)}
								error={!!otpError}
								helperText={otpError}
								size="small"
							/>
							<Button
								variant="contained"
								onClick={handleConfirmOTP}
							>
								Xác nhận
							</Button>
						</Box>
					)}
					{twoFAEnabled && !showOTPForm && (
						<Typography
							color="success.main"
							mt={2}
						>
							2FA đang hoạt động cho tài khoản này.
						</Typography>
					)}
				</Paper>

				{/* Quản lý thiết bị */}
				<Paper
					sx={{ p: 3 }}
					variant="outlined"
				>
					<Typography
						variant="h6"
						fontWeight={600}
						mb={1}
					>
						Quản lý đăng nhập thiết bị
					</Typography>
					<Typography
						color="text.secondary"
						mb={2}
					>
						Xem và quản lý các thiết bị đã đăng nhập vào tài khoản của bạn.
					</Typography>
					<List>
						{devices.map((device) => (
							<ListItem
								key={device.id}
								secondaryAction={
									!device.current && (
										<Button
											variant="outlined"
											color="error"
											size="small"
											onClick={() => handleLogoutDevice(device.id)}
										>
											Đăng xuất
										</Button>
									)
								}
							>
								<ListItemText
									primary={device.name + (device.current ? ' (Thiết bị hiện tại)' : '')}
									secondary={device.location}
								/>
							</ListItem>
						))}
					</List>
				</Paper>
			</Box>
		</Box>
	);
}
