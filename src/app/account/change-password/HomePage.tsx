// app/my-account/page.tsx
'use client';
import { Box, Typography, TextField, Paper, Button, Divider } from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

const schema = z
	.object({
		currentPassword: z.string().min(1, 'Vui lòng nhập mật khẩu hiện tại'),
		newPassword: z.string().min(6, 'Mật khẩu mới phải từ 6 ký tự').max(100, 'Mật khẩu mới tối đa 100 ký tự'),
		confirmPassword: z.string()
	})
	.refine((data) => data.newPassword !== data.currentPassword, {
		message: 'Mật khẩu mới phải khác mật khẩu cũ',
		path: ['newPassword']
	})
	.refine((data) => data.newPassword === data.confirmPassword, {
		message: 'Nhập lại mật khẩu mới không khớp',
		path: ['confirmPassword']
	});

type FormData = z.infer<typeof schema>;

export default function MyAccountPage() {
	const {
		control,
		handleSubmit,
		formState: { errors, isDirty, isValid }
	} = useForm<FormData>({
		resolver: zodResolver(schema),
		defaultValues: {
			currentPassword: '',
			newPassword: '',
			confirmPassword: ''
		},
		mode: 'onChange'
	});

	const onSubmit = (data: FormData) => {
		// Xử lý đổi mật khẩu ở đây
		console.log('Đổi mật khẩu:', data);
	};

	return (
		<Box
			py={4}
			display="flex"
			justifyContent="center"
			alignItems="center"
		>
			<Box sx={{ width: '100%' }}>
				<form
					onSubmit={handleSubmit(onSubmit)}
					noValidate
				>
					<Typography
						variant="h5"
						fontWeight={700}
						mb={2}
					>
						Đổi mật khẩu
					</Typography>
					<Typography
						color="text.secondary"
						mb={3}
					>
						Bạn có thể cập nhật tài khoản của mình ở đây.
					</Typography>
					<Divider sx={{ my: 3 }} />

					<Typography
						fontWeight={500}
						mb={1}
					>
						Mật khẩu hiện tại
					</Typography>
					<Controller
						name="currentPassword"
						control={control}
						render={({ field }) => (
							<TextField
								{...field}
								type="password"
								fullWidth
								margin="dense"
								error={!!errors.currentPassword}
								helperText={errors.currentPassword?.message}
							/>
						)}
					/>

					<Typography
						fontWeight={500}
						mt={2}
						mb={1}
					>
						Mật khẩu mới
					</Typography>
					<Controller
						name="newPassword"
						control={control}
						render={({ field }) => (
							<TextField
								{...field}
								type="password"
								fullWidth
								margin="dense"
								error={!!errors.newPassword}
								helperText={
									errors.newPassword?.message ||
									'Mật khẩu mới nên có từ 6 - 100 ký tự, và khác mật khẩu cũ'
								}
							/>
						)}
					/>

					<Typography
						fontWeight={500}
						mt={2}
						mb={1}
					>
						Nhập lại mật khẩu mới
					</Typography>
					<Controller
						name="confirmPassword"
						control={control}
						render={({ field }) => (
							<TextField
								{...field}
								type="password"
								fullWidth
								margin="dense"
								error={!!errors.confirmPassword}
								helperText={errors.confirmPassword?.message}
							/>
						)}
					/>

					<Button
						type="submit"
						variant="contained"
						sx={{ minWidth: 120, alignSelf: 'flex-end', mt: 3 }}
						disabled={!isDirty || !isValid}
					>
						Đổi mật khẩu
					</Button>
				</form>
			</Box>
		</Box>
	);
}
