'use client';
import {
	Box,
	Typography,
	Paper,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Link,
	Divider
} from '@mui/material';

export default function InvoicePage() {
	return (
		<Box>
			<Typography
				variant="h5"
				fontWeight={700}
				mb={2}
			>
				Hóa đơn
			</Typography>

			<Typography
				color="text.secondary"
				mb={2}
			>
				Phương thức thanh toán bạn đã lưu sẽ tự động được tính phí vào ngày đến hạn thanh toán hóa đơn.
			</Typography>

			<Divider sx={{ my: 3 }} />
			<Typography
				color="primary"
				fontSize={14}
				mb={3}
			>
				Tìm hiểu thêm trong hóa đơn và chứng từ thanh toán của chúng tôi.
			</Typography>

			<TableContainer
				component={Paper}
				elevation={0}
				sx={{ boxShadow: 'none', mb: 3 }}
			>
				<Table>
					<TableHead>
						<TableRow>
							<TableCell>
								<b><PERSON>ố hóa đơn</b>
							</TableCell>
							<TableCell>
								<b>Trạng thái</b>
							</TableCell>
							<TableCell>
								<b>Ngày</b>
							</TableCell>
							<TableCell align="right">
								<b>Số lượng</b>
							</TableCell>
						</TableRow>
					</TableHead>
					<TableBody>
						<TableRow>
							<TableCell>1751371200</TableCell>
							<TableCell>
								<Box
									component="span"
									sx={{ color: '#f59e42', fontWeight: 500 }}
								>
									Sắp tới
								</Box>
							</TableCell>
							<TableCell>Ngày 01 tháng 07 năm 2025</TableCell>
							<TableCell align="right">0,00 đô la</TableCell>
						</TableRow>
					</TableBody>
				</Table>
			</TableContainer>

			<Paper
				elevation={0}
				sx={{ p: 2, background: '#f6f8fa', boxShadow: 'none' }}
			>
				<Typography>
					Bạn có thắc mắc về hóa đơn của mình không?{' '}
					<Link
						href="#"
						color="primary"
						fontWeight={500}
					>
						Bắt đầu tại đây!
					</Link>
				</Typography>
			</Paper>
		</Box>
	);
}
