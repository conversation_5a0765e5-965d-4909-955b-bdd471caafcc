// app/my-account/page.tsx
'use client';
import { Box, Typography, TextField, Paper, Divider, Button } from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

const schema = z.object({
	name: z.string().min(1, 'Tên không được để trống'),
	phone: z
		.string()
		.optional()
		.refine((val) => !val || /^[0-9]+$/.test(val), {
			message: 'Số điện thoại chỉ được chứa số'
		})
});

type FormData = z.infer<typeof schema>;

export default function MyAccountPage() {
	const {
		control,
		handleSubmit,
		formState: { errors, isDirty, isValid }
	} = useForm<FormData>({
		resolver: zodResolver(schema),
		defaultValues: {
			name: 'phan thanh cong',
			phone: ''
		},
		mode: 'onChange'
	});

	const onSubmit = (data: FormData) => {
		// Xử lý cập nhật thông tin ở đây
		console.log('Form data:', data);
	};

	return (
		<Box py={4}>
			<Box sx={{ width: '100%' }}>
				<form
					onSubmit={handleSubmit(onSubmit)}
					noValidate
				>
					<Typography
						variant="h5"
						fontWeight={700}
						mb={2}
					>
						Thông tin tài khoản
					</Typography>
					<Typography
						color="text.secondary"
						mb={3}
					>
						Bạn có thể chỉnh sửa thông tin tài khoản của mình ở đây.
					</Typography>

					<Divider sx={{ my: 3 }} />
					<Typography
						fontWeight={500}
						mb={1}
					>
						Tên
					</Typography>
					<Controller
						name="name"
						control={control}
						render={({ field }) => (
							<TextField
								{...field}
								fullWidth
								placeholder="Tên của bạn"
								margin="dense"
								error={!!errors.name}
								helperText={errors.name?.message}
							/>
						)}
					/>
					<Typography
						variant="caption"
						color="text.secondary"
						mb={2}
						display="block"
					>
						Tên này sẽ được hiển thị trên trang cá nhân của bạn.
					</Typography>

					<Typography
						fontWeight={500}
						mt={2}
						mb={1}
					>
						Email
					</Typography>
					<TextField
						fullWidth
						value="<EMAIL>"
						disabled
						margin="dense"
					/>
					<Typography
						variant="caption"
						color="text.secondary"
						mb={2}
						display="block"
					>
						Bạn không thể thay đổi email (nếu muốn thay đổi, vui lòng liên hệ quản trị viên)
					</Typography>

					<Typography
						fontWeight={500}
						mt={2}
						mb={1}
					>
						Số điện thoại
					</Typography>
					<Controller
						name="phone"
						control={control}
						render={({ field }) => (
							<TextField
								{...field}
								fullWidth
								placeholder="Số điện thoại"
								margin="dense"
								error={!!errors.phone}
								helperText={errors.phone?.message}
							/>
						)}
					/>

					<Button
						variant="contained"
						color="secondary"
						aria-label="Sign in"
						type="submit"
						size="large"
						disabled={!isDirty || !isValid}
						sx={{ minWidth: 120, alignSelf: 'flex-end', mt: 2 }}
					>
						Cập nhật tài khoản
					</Button>
				</form>
			</Box>
		</Box>
	);
}
