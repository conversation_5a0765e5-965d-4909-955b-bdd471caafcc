'use client';
import MainLayout from 'src/components/MainLayout';
import AuthGuardRedirect from '@auth/AuthGuardRedirect';
import authRoles from '@auth/authRoles';
import { usePathname } from 'next/navigation';
import Sidebar from '@/components/account/Sidebar';
import { Box } from '@mui/system';
import { Drawer, IconButton } from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import { useState } from 'react';

function Layout({ children }) {
	const pathname = usePathname();
	console.log('Current Pathname:', pathname);
	const [drawerOpen, setDrawerOpen] = useState(false);

	return (
		<AuthGuardRedirect auth={authRoles.onlyGuest}>
			<MainLayout
				navbar={true}
				toolbar={true}
				leftSidePanel={true}
				rightSidePanel={false}
				footer={false}
			>
				{/* <Box
					className="bg-white h-full"
					sx={{ display: 'flex', minHeight: '100vh' }}
				> */}
				{/* <Box sx={{ display: { xs: 'none', md: 'flex' } }}>
						<Sidebar />
					</Box>
					<IconButton
						onClick={() => setDrawerOpen(true)}
						sx={{
							display: { xs: 'block', md: 'none' },
							position: 'fixed',
							top: 16,
							left: 16,
							zIndex: 1301
						}}
					>
						<MenuIcon />
					</IconButton>

					<Drawer
						open={drawerOpen}
						onClose={() => setDrawerOpen(false)}
						sx={{ display: { xs: 'block', md: 'none' } }}
					>
						<Box sx={{ width: 240 }}>
							<Sidebar onCloseDrawer={() => setDrawerOpen(false)} />
						</Box>
					</Drawer> */}
				<Box
					sx={{
						flex: 1,
						display: 'flex',
						flexDirection: 'column',
						px: { xs: 2, md: 6, lg: 12 },
						pt: { xs: 9, md: 6 }
					}}
				>
					{children}
				</Box>
				{/* </Box> */}
			</MainLayout>
		</AuthGuardRedirect>
	);
}

export default Layout;
