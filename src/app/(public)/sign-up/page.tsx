'use client';

import authRoles from '@auth/authRoles';
import AuthGuardRedirect from '@auth/AuthGuardRedirect';
import SignUpPage from './SignUpPage';
import { useContext } from 'react';
import { AppContext } from '@/contexts/AppContext';

function Page() {
	const { user, setUser } = useContext(AppContext);
	console.log(user);

	return (
		<AuthGuardRedirect auth={authRoles.onlyGuest}>
			<SignUpPage />
		</AuthGuardRedirect>
	);
}

export default Page;
