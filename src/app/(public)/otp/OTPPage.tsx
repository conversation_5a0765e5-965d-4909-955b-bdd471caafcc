'use client';

import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { Box } from '@mui/system';
import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import CardContent from '@mui/material/CardContent';
import AvatarGroup from '@mui/material/AvatarGroup';
import Avatar from '@mui/material/Avatar';

import OtpInput from 'react-otp-input';
import Button from '@mui/material/Button';
import { Alert } from '@mui/material';
import { useRouter } from 'next/navigation';
import { useSnackbar } from 'notistack';
import http from '@/lib/http';

function OtpPage() {
	const searchParams = useSearchParams();
	const rawEmail = searchParams.get('email');
	const email = rawEmail?.replace(/ /g, '+');

	const [errors, setError] = useState<{ type: string; message: string } | null>(null);
	const router = useRouter();

	const { enqueueSnackbar } = useSnackbar();

	useEffect(() => {
		if (email) {
			handleSendOtp();
		}
	}, [email]);

	const handleSendOtp = async () => {
		try {
			const response = await http.post('/api-web/api-gateway/auth/email/send-code-verify', {
				email
			});

			if (response.statusCode === 201) {
				enqueueSnackbar('Đã gửi mã code', { variant: 'success' });
			} else {
				setError({
					type: 'manual',
					message: response.message || 'Có lỗi xảy ra. Vui lòng thử lại.'
				});
			}
		} catch (error) {
			setError({ type: 'manual', message: error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.' });
		}
	};

	const handleVerifyEmail = async () => {
		try {
			const response = await http.post('/api-web/api-gateway/auth/email/verify', {
				email,
				code: otp
			});

			if (response.statusCode === 201) {
				router.push('sign-in');
			}

			setError({
				type: 'manual',
				message: response?.message || 'Có lỗi xảy ra. Vui lòng thử lại.'
			});
		} catch (error) {
			setError({ type: 'manual', message: error?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.' });
		}
	};

	const [otp, setOtp] = useState('');

	return (
		<div className="flex min-w-0 flex-1 flex-col items-center sm:flex-row sm:justify-center md:items-start md:justify-start">
			<Paper className="h-full w-full px-4 py-2 ltr:border-r-1 rtl:border-l-1 sm:h-auto sm:w-auto sm:rounded-xl sm:p-12 sm:shadow-sm md:flex md:h-full md:w-1/2 md:items-center md:justify-end md:rounded-none md:p-16 md:shadow-none">
				<CardContent className="mx-auto w-full max-w-80 sm:mx-0 sm:w-80">
					<img
						className="w-12"
						src="/assets/images/logo/logo.svg"
						alt="logo"
					/>

					<Typography className="mt-8 text-4xl font-extrabold leading-[1.25] tracking-tight">
						Verify Account
					</Typography>

					<div className="mt-0.5 flex items-baseline font-medium">
						<Typography>An OTP has been sent to your entered email {email}</Typography>
						<Box
							component="span"
							className="font-bold"
						></Box>
					</div>
					{errors?.message && (
						<Alert
							className="mt-4"
							severity="error"
							sx={(theme) => ({
								backgroundColor: theme.palette.error.light,
								color: theme.palette.error.dark
							})}
						>
							{errors?.message}
						</Alert>
					)}
					<Box className="mt-8">
						<OtpInput
							value={otp}
							onChange={setOtp}
							numInputs={6}
							inputType="tel"
							inputStyle="!w-8 h-12 rounded-lg border-0 mx-2 bg-gray-300 text-xl bg-red"
							renderInput={(props) => <input {...props} />}
						/>
					</Box>

					<Typography className="text-gray-700 text-center mt-8">
						Didn't receive OTP code? {'  '}
						<Box
							component="span"
							className="underline cursor-pointer text-blue-600"
							onClick={handleSendOtp}
						>
							Resend code
						</Box>
					</Typography>

					<Button
						variant="contained"
						color="secondary"
						className="mt-4 w-full"
						aria-label="Confirm"
						type="submit"
						size="large"
						onClick={handleVerifyEmail}
					>
						Confirm
					</Button>
				</CardContent>
			</Paper>

			<Box
				className="relative hidden h-full flex-auto items-center justify-center overflow-hidden p-16 md:flex lg:px-28"
				sx={{ backgroundColor: 'primary.main' }}
			>
				<svg
					className="pointer-events-none absolute inset-0"
					viewBox="0 0 960 540"
					width="100%"
					height="100%"
					preserveAspectRatio="xMidYMax slice"
					xmlns="http://www.w3.org/2000/svg"
				>
					<Box
						component="g"
						className="opacity-5"
						fill="none"
						stroke="currentColor"
						strokeWidth="100"
					>
						<circle
							r="234"
							cx="196"
							cy="23"
						/>
						<circle
							r="234"
							cx="790"
							cy="491"
						/>
					</Box>
				</svg>
				<Box
					component="svg"
					className="absolute -right-16 -top-16 opacity-20"
					sx={{ color: 'primary.light' }}
					viewBox="0 0 220 192"
					width="220px"
					height="192px"
					fill="none"
				>
					<defs>
						<pattern
							id="837c3e70-6c3a-44e6-8854-cc48c737b659"
							x="0"
							y="0"
							width="20"
							height="20"
							patternUnits="userSpaceOnUse"
						>
							<rect
								x="0"
								y="0"
								width="4"
								height="4"
								fill="currentColor"
							/>
						</pattern>
					</defs>
					<rect
						width="220"
						height="192"
						fill="url(#837c3e70-6c3a-44e6-8854-cc48c737b659)"
					/>
				</Box>

				<div className="relative z-10 w-full max-w-4xl ">
					<div className="text-7xl font-bold leading-none text-gray-100">
						<div>Chào mừng đến với</div>
						<div>GTEL MAPS</div>
					</div>
					<div className="mt-6 text-lg leading-6 tracking-tight text-white">
						Bạn có cảm thấy mệt mỏi khi lạc đường hoặc bị kẹt xe không? Hãy nói lời tạm biệt với lo lắng về
						điều hướng với GTEL MAPS đối tác hành trình hoàn hảo dành riêng cho Việt Nam!
					</div>
					<div className="mt-8 flex items-center">
						<AvatarGroup
							sx={{
								'& .MuiAvatar-root': {
									borderColor: 'primary.main'
								}
							}}
						>
							<Avatar src="/assets/images/avatars/female-18.jpg" />
							<Avatar src="/assets/images/avatars/female-11.jpg" />
							<Avatar src="/assets/images/avatars/male-09.jpg" />
							<Avatar src="/assets/images/avatars/male-16.jpg" />
						</AvatarGroup>

						<div className="ml-4 font-medium tracking-tight text-white">
							Đã có hơn hàng nghìn người tham gia cùng chúng tôi, đến lượt của bạn
						</div>
					</div>
				</div>
			</Box>
		</div>
	);
}

export default OtpPage;
